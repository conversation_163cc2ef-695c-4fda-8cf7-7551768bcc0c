import React from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '../../contexts/AuthContext'
import {
  TrendingUp,
  Users,
  BookOpen,
  Award,
  Plus,
  ArrowRight,
  Calendar,
  Clock,
  Target,
  Zap
} from 'lucide-react'
import { Link } from 'react-router-dom'

const Dashboard = () => {
  const { user } = useAuth()

  const stats = [
    {
      name: 'Active Projects',
      value: '8',
      change: '+12%',
      changeType: 'increase',
      icon: BookOpen,
      color: 'bg-blue-500'
    },
    {
      name: 'Team Members',
      value: '24',
      change: '+8%',
      changeType: 'increase',
      icon: Users,
      color: 'bg-green-500'
    },
    {
      name: 'Research Papers',
      value: '3',
      change: '+1',
      changeType: 'increase',
      icon: TrendingUp,
      color: 'bg-purple-500'
    },
    {
      name: 'Badges Earned',
      value: '15',
      change: '+3',
      changeType: 'increase',
      icon: Award,
      color: 'bg-yellow-500'
    }
  ]

  const recentProjects = [
    {
      id: 1,
      title: 'AI-Powered Learning Assistant',
      description: 'Machine learning project for educational enhancement',
      status: 'In Progress',
      progress: 75,
      dueDate: '2024-01-15',
      team: ['<PERSON>', '<PERSON>', '<PERSON>'],
      priority: 'high'
    },
    {
      id: 2,
      title: 'Blockchain Voting System',
      description: 'Secure voting platform using blockchain technology',
      status: 'Review',
      progress: 90,
      dueDate: '2024-01-10',
      team: ['David', 'Eve'],
      priority: 'medium'
    },
    {
      id: 3,
      title: 'IoT Smart Campus',
      description: 'Internet of Things implementation for campus management',
      status: 'Planning',
      progress: 25,
      dueDate: '2024-02-01',
      team: ['Frank', 'Grace', 'Henry', 'Ivy'],
      priority: 'low'
    }
  ]

  const upcomingDeadlines = [
    {
      title: 'Project Review - AI Assistant',
      date: '2024-01-08',
      time: '2:00 PM',
      type: 'review'
    },
    {
      title: 'Research Paper Submission',
      date: '2024-01-12',
      time: '11:59 PM',
      type: 'submission'
    },
    {
      title: 'Team Meeting - Blockchain',
      date: '2024-01-09',
      time: '10:00 AM',
      type: 'meeting'
    }
  ]

  const quickActions = [
    {
      title: 'Create New Project',
      description: 'Start a new project with AI assistance',
      icon: Plus,
      href: '/projects/create',
      color: 'bg-blue-500'
    },
    {
      title: 'Join a Team',
      description: 'Find and join collaborative teams',
      icon: Users,
      href: '/teams',
      color: 'bg-green-500'
    },
    {
      title: 'AI Research Assistant',
      description: 'Get help with research and writing',
      icon: Zap,
      href: '/ai-assistant',
      color: 'bg-purple-500'
    },
    {
      title: 'View Analytics',
      description: 'Track your progress and performance',
      icon: TrendingUp,
      href: '/analytics',
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user?.name?.split(' ')[0]}! 👋
            </h1>
            <p className="text-primary-100 text-lg">
              Ready to make some progress today? You have 3 pending tasks.
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center">
              <Target className="w-16 h-16 text-white/80" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="card p-6 hover:shadow-lg transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
                <p className={`text-sm ${
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change} from last month
                </p>
              </div>
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Projects */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="lg:col-span-2"
        >
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Recent Projects
              </h2>
              <Link
                to="/projects"
                className="text-primary-600 hover:text-primary-700 font-medium flex items-center"
              >
                View all
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div
                  key={project.id}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {project.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {project.description}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                      project.status === 'Review' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex -space-x-2">
                        {project.team.slice(0, 3).map((member, idx) => (
                          <div
                            key={idx}
                            className="w-6 h-6 bg-gray-300 rounded-full border-2 border-white flex items-center justify-center text-xs font-medium"
                          >
                            {member[0]}
                          </div>
                        ))}
                        {project.team.length > 3 && (
                          <div className="w-6 h-6 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs">
                            +{project.team.length - 3}
                          </div>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        Due {project.dueDate}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-600 h-2 rounded-full"
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">
                        {project.progress}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Sidebar */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-6"
        >
          {/* Upcoming Deadlines */}
          <div className="card p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Upcoming Deadlines
            </h3>
            <div className="space-y-3">
              {upcomingDeadlines.map((deadline, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    deadline.type === 'review' ? 'bg-blue-500' :
                    deadline.type === 'submission' ? 'bg-red-500' :
                    'bg-green-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {deadline.title}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Calendar className="w-3 h-3" />
                      <span>{deadline.date}</span>
                      <Clock className="w-3 h-3" />
                      <span>{deadline.time}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="space-y-3">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.href}
                  className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
                >
                  <div className={`${action.color} p-2 rounded-lg mr-3 group-hover:scale-110 transition-transform`}>
                    <action.icon className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {action.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {action.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
