import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'

// Layout Components
import Layout from './components/Layout/Layout'
import AuthLayout from './components/Layout/AuthLayout'

// Auth Pages
import Login from './pages/Auth/Login'
import Register from './pages/Auth/Register'
import ForgotPassword from './pages/Auth/ForgotPassword'

// Dashboard Pages
import Dashboard from './pages/Dashboard/Dashboard'
import ProjectHub from './pages/Projects/ProjectHub'
import ProjectDetails from './pages/Projects/ProjectDetails'
import CreateProject from './pages/Projects/CreateProject'

// Research Pages
import ResearchFlow from './pages/Research/ResearchFlow'
import ResearchDetails from './pages/Research/ResearchDetails'
import AIAssistant from './pages/Research/AIAssistant'

// Team Pages
import TeamBuilder from './pages/Team/TeamBuilder'
import TeamDetails from './pages/Team/TeamDetails'
import Chat from './pages/Team/Chat'

// Analytics Pages
import Analytics from './pages/Analytics/Analytics'
import Leaderboard from './pages/Analytics/Leaderboard'

// Profile Pages
import Profile from './pages/Profile/Profile'
import Settings from './pages/Profile/Settings'

// Admin Pages
import AdminDashboard from './pages/Admin/AdminDashboard'
import UserManagement from './pages/Admin/UserManagement'

// Context Providers
import { AuthProvider } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'

// Protected Route Component
import ProtectedRoute from './components/Auth/ProtectedRoute'

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
            <AnimatePresence mode="wait">
              <Routes>
                {/* Auth Routes */}
                <Route path="/auth" element={<AuthLayout />}>
                  <Route path="login" element={<Login />} />
                  <Route path="register" element={<Register />} />
                  <Route path="forgot-password" element={<ForgotPassword />} />
                </Route>

                {/* Protected Routes */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<Dashboard />} />
                  
                  {/* Project Routes */}
                  <Route path="projects" element={<ProjectHub />} />
                  <Route path="projects/create" element={<CreateProject />} />
                  <Route path="projects/:id" element={<ProjectDetails />} />
                  
                  {/* Research Routes */}
                  <Route path="research" element={<ResearchFlow />} />
                  <Route path="research/:id" element={<ResearchDetails />} />
                  <Route path="ai-assistant" element={<AIAssistant />} />
                  
                  {/* Team Routes */}
                  <Route path="teams" element={<TeamBuilder />} />
                  <Route path="teams/:id" element={<TeamDetails />} />
                  <Route path="chat" element={<Chat />} />
                  
                  {/* Analytics Routes */}
                  <Route path="analytics" element={<Analytics />} />
                  <Route path="leaderboard" element={<Leaderboard />} />
                  
                  {/* Profile Routes */}
                  <Route path="profile" element={<Profile />} />
                  <Route path="settings" element={<Settings />} />
                  
                  {/* Admin Routes */}
                  <Route path="admin" element={<AdminDashboard />} />
                  <Route path="admin/users" element={<UserManagement />} />
                </Route>

                {/* Fallback */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </AnimatePresence>
            
            {/* Global Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  theme: {
                    primary: '#22c55e',
                    secondary: '#black',
                  },
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
