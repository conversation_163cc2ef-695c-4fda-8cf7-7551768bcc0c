import React from 'react'
import { NavLink } from 'react-router-dom'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import {
  Home,
  Folder,
  FlaskConical,
  Users,
  BarChart3,
  Trophy,
  User,
  Settings,
  Shield,
  MessageCircle,
  Sparkles
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Projects', href: '/projects', icon: Folder },
  { name: 'Research', href: '/research', icon: FlaskConical },
  { name: 'AI Assistant', href: '/ai-assistant', icon: Sparkles },
  { name: 'Teams', href: '/teams', icon: Users },
  { name: 'Chat', href: '/chat', icon: MessageCircle },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Leaderboard', href: '/leaderboard', icon: Trophy },
  { name: 'Profile', href: '/profile', icon: User },
  { name: 'Settings', href: '/settings', icon: Settings },
  { name: 'Admin', href: '/admin', icon: Shield },
]

const MobileMenu = ({ onClose }) => {
  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 shadow-xl">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">PS</span>
          </div>
          <span className="ml-2 text-lg font-bold text-gradient">ProjectSphere</span>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            onClick={onClose}
            className={({ isActive }) =>
              `flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 hover:text-primary-700 transition-all duration-200 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-primary-400 ${
                isActive ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : ''
              }`
            }
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            <span className="ml-3 text-sm font-medium">{item.name}</span>
          </NavLink>
        ))}
      </nav>
    </div>
  )
}

export default MobileMenu
