import React from 'react'
import { motion } from 'framer-motion'
import { FlaskConical, BookOpen, FileText, Award } from 'lucide-react'

const ResearchFlow = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Research Flow
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage your research papers and publication workflow
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { icon: FlaskConical, title: 'Research Ideas', count: '12', color: 'bg-blue-500' },
          { icon: BookOpen, title: 'In Progress', count: '5', color: 'bg-yellow-500' },
          { icon: FileText, title: 'Under Review', count: '3', color: 'bg-purple-500' },
          { icon: Award, title: 'Published', count: '8', color: 'bg-green-500' }
        ].map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="card p-6 text-center"
          >
            <div className={`${item.color} w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4`}>
              <item.icon className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{item.count}</h3>
            <p className="text-gray-600 dark:text-gray-400">{item.title}</p>
          </motion.div>
        ))}
      </div>

      <div className="card p-8 text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Complete research paper management system with AI assistance, peer review, and publication tracking.
        </p>
      </div>
    </motion.div>
  )
}

export default ResearchFlow
