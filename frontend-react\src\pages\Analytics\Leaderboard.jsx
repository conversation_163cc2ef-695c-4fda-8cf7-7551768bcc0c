import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Trophy, Medal, Award, Star, TrendingUp, Users, Target } from 'lucide-react'

const Leaderboard = () => {
  const [activeTab, setActiveTab] = useState('overall')

  const leaderboardData = {
    overall: [
      { rank: 1, name: '<PERSON>', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', score: 2850, projects: 15, badges: 12, department: 'Computer Science' },
      { rank: 2, name: '<PERSON>', avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&h=150&fit=crop&crop=face', score: 2720, projects: 12, badges: 10, department: 'Engineering' },
      { rank: 3, name: '<PERSON>', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face', score: 2650, projects: 14, badges: 9, department: 'Computer Science' },
      { rank: 4, name: '<PERSON>', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', score: 2580, projects: 11, badges: 11, department: 'Business' },
      { rank: 5, name: 'Eve Davis', avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face', score: 2450, projects: 10, badges: 8, department: 'Design' },
      { rank: 6, name: 'Frank Miller', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face', score: 2380, projects: 9, badges: 7, department: 'Engineering' },
      { rank: 7, name: 'Grace Lee', avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face', score: 2320, projects: 8, badges: 9, department: 'Computer Science' },
      { rank: 8, name: 'Henry Taylor', avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face', score: 2250, projects: 7, badges: 6, department: 'Business' }
    ],
    projects: [
      { rank: 1, name: 'Charlie Brown', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face', score: 14, projects: 14, badges: 9, department: 'Computer Science' },
      { rank: 2, name: 'Alice Johnson', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', score: 15, projects: 15, badges: 12, department: 'Computer Science' },
      { rank: 3, name: 'Bob Smith', avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&h=150&fit=crop&crop=face', score: 12, projects: 12, badges: 10, department: 'Engineering' }
    ],
    research: [
      { rank: 1, name: 'Alice Johnson', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', score: 8, projects: 15, badges: 12, department: 'Computer Science' },
      { rank: 2, name: 'Diana Prince', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', score: 6, projects: 11, badges: 11, department: 'Business' },
      { rank: 3, name: 'Bob Smith', avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=150&h=150&fit=crop&crop=face', score: 5, projects: 12, badges: 10, department: 'Engineering' }
    ]
  }

  const tabs = [
    { id: 'overall', name: 'Overall', icon: Trophy },
    { id: 'projects', name: 'Projects', icon: Target },
    { id: 'research', name: 'Research', icon: Award }
  ]

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</span>
    }
  }

  const getRankBg = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600'
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500'
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600'
      default:
        return 'bg-gray-100 dark:bg-gray-700'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Leaderboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          See how you rank among your peers
        </p>
      </div>

      {/* Tabs */}
      <div className="card p-2">
        <div className="flex space-x-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Top 3 Podium */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="card p-8"
      >
        <div className="flex items-end justify-center space-x-8">
          {/* 2nd Place */}
          {leaderboardData[activeTab][1] && (
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="relative mb-4">
                <img
                  src={leaderboardData[activeTab][1].avatar}
                  alt={leaderboardData[activeTab][1].name}
                  className="w-20 h-20 rounded-full object-cover mx-auto border-4 border-gray-300"
                />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
              </div>
              <div className="bg-gray-200 dark:bg-gray-700 rounded-lg p-4 h-24 flex flex-col justify-end">
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                  {leaderboardData[activeTab][1].name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs">
                  {leaderboardData[activeTab][1].score} points
                </p>
              </div>
            </motion.div>
          )}

          {/* 1st Place */}
          {leaderboardData[activeTab][0] && (
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="relative mb-4">
                <img
                  src={leaderboardData[activeTab][0].avatar}
                  alt={leaderboardData[activeTab][0].name}
                  className="w-24 h-24 rounded-full object-cover mx-auto border-4 border-yellow-400"
                />
                <div className="absolute -top-3 -right-3 w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                  <Trophy className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg p-4 h-32 flex flex-col justify-end text-white">
                <h3 className="font-bold text-lg">
                  {leaderboardData[activeTab][0].name}
                </h3>
                <p className="text-yellow-100">
                  {leaderboardData[activeTab][0].score} points
                </p>
              </div>
            </motion.div>
          )}

          {/* 3rd Place */}
          {leaderboardData[activeTab][2] && (
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-center"
            >
              <div className="relative mb-4">
                <img
                  src={leaderboardData[activeTab][2].avatar}
                  alt={leaderboardData[activeTab][2].name}
                  className="w-20 h-20 rounded-full object-cover mx-auto border-4 border-amber-500"
                />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
              </div>
              <div className="bg-amber-100 dark:bg-amber-900/30 rounded-lg p-4 h-24 flex flex-col justify-end">
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                  {leaderboardData[activeTab][2].name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-xs">
                  {leaderboardData[activeTab][2].score} points
                </p>
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Full Leaderboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="card overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Full Rankings
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {leaderboardData[activeTab].map((user, index) => (
            <motion.div
              key={user.rank}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.7 + index * 0.05 }}
              className={`p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                user.rank <= 3 ? getRankBg(user.rank) + ' text-white' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8">
                    {getRankIcon(user.rank)}
                  </div>
                  
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  
                  <div>
                    <h4 className={`font-semibold ${user.rank <= 3 ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {user.name}
                    </h4>
                    <p className={`text-sm ${user.rank <= 3 ? 'text-white/80' : 'text-gray-600 dark:text-gray-400'}`}>
                      {user.department}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <p className={`font-bold ${user.rank <= 3 ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {user.score}
                    </p>
                    <p className={`${user.rank <= 3 ? 'text-white/80' : 'text-gray-600 dark:text-gray-400'}`}>
                      Points
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <p className={`font-bold ${user.rank <= 3 ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {user.projects}
                    </p>
                    <p className={`${user.rank <= 3 ? 'text-white/80' : 'text-gray-600 dark:text-gray-400'}`}>
                      Projects
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <p className={`font-bold ${user.rank <= 3 ? 'text-white' : 'text-gray-900 dark:text-white'}`}>
                      {user.badges}
                    </p>
                    <p className={`${user.rank <= 3 ? 'text-white/80' : 'text-gray-600 dark:text-gray-400'}`}>
                      Badges
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Your Rank */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="card p-6 bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold">
              #12
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white">Your Current Rank</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">Keep going to climb higher!</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-primary-600">2,180</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">points</p>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default Leaderboard
