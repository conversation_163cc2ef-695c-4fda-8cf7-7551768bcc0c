import React, { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios'
import toast from 'react-hot-toast'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Mock user data for development
  const mockUser = {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'student',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    department: 'Computer Science',
    semester: '6th',
    university: 'Tech University',
    joinedAt: '2023-08-15',
    stats: {
      projectsCompleted: 12,
      researchPapers: 3,
      teamCollaborations: 8,
      badges: 15
    }
  }

  useEffect(() => {
    // Check for existing session
    const token = localStorage.getItem('token')
    const userData = localStorage.getItem('user')
    
    if (token && userData) {
      try {
        setUser(JSON.parse(userData))
        setIsAuthenticated(true)
        // Set axios default header
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } catch (error) {
        console.error('Error parsing user data:', error)
        logout()
      }
    }
    setLoading(false)
  }, [])

  const login = async (email, password) => {
    try {
      setLoading(true)
      
      // Mock login for development
      if (email && password) {
        const token = 'mock-jwt-token-' + Date.now()
        
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(mockUser))
        
        setUser(mockUser)
        setIsAuthenticated(true)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        toast.success('Welcome back!')
        return { success: true }
      }
      
      throw new Error('Invalid credentials')
    } catch (error) {
      toast.error(error.message || 'Login failed')
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    try {
      setLoading(true)
      
      // Mock registration
      const newUser = {
        ...mockUser,
        ...userData,
        id: 'user-' + Date.now()
      }
      
      const token = 'mock-jwt-token-' + Date.now()
      
      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(newUser))
      
      setUser(newUser)
      setIsAuthenticated(true)
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      
      toast.success('Account created successfully!')
      return { success: true }
    } catch (error) {
      toast.error(error.message || 'Registration failed')
      return { success: false, error: error.message }
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    delete axios.defaults.headers.common['Authorization']
    setUser(null)
    setIsAuthenticated(false)
    toast.success('Logged out successfully')
  }

  const updateProfile = async (updates) => {
    try {
      const updatedUser = { ...user, ...updates }
      localStorage.setItem('user', JSON.stringify(updatedUser))
      setUser(updatedUser)
      toast.success('Profile updated successfully')
      return { success: true }
    } catch (error) {
      toast.error('Failed to update profile')
      return { success: false, error: error.message }
    }
  }

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
