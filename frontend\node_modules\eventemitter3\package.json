{"name": "eventemitter3", "version": "5.0.1", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "main": "index.js", "types": "index.d.ts", "scripts": {"rollup": "rimraf dist umd && rollup -c", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "c8 --reporter=lcov --reporter=text mocha test/test.js", "test-esm": "mocha test/test.mjs", "prepublishOnly": "npm run rollup", "test-browser": "node test/browser.js"}, "files": ["index.js", "index.mjs", "index.d.ts", "dist"], "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-terser": "^0.4.0", "assume": "^2.2.0", "c8": "^7.3.1", "mocha": "^10.0.0", "pre-commit": "^1.2.0", "rimraf": "^4.1.2", "rollup": "^3.4.0", "sauce-browsers": "^3.0.0", "sauce-test": "^1.3.3"}}