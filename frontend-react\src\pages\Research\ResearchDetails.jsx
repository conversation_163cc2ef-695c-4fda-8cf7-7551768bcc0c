import React from 'react'
import { motion } from 'framer-motion'
import { useParams } from 'react-router-dom'

const ResearchDetails = () => {
  const { id } = useParams()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="card p-8 text-center"
    >
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
        Research Paper Details
      </h1>
      <p className="text-gray-600 dark:text-gray-400">
        Research paper ID: {id}
      </p>
    </motion.div>
  )
}

export default ResearchDetails
