import React, { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export const ThemeProvider = ({ children }) => {
  const [isDark, setIsDark] = useState(false)
  const [primaryColor, setPrimaryColor] = useState('blue')
  const [accentColor, setAccentColor] = useState('purple')

  useEffect(() => {
    // Check for saved theme preference or default to system preference
    const savedTheme = localStorage.getItem('theme')
    const savedPrimaryColor = localStorage.getItem('primaryColor')
    const savedAccentColor = localStorage.getItem('accentColor')
    
    if (savedTheme) {
      setIsDark(savedTheme === 'dark')
    } else {
      setIsDark(window.matchMedia('(prefers-color-scheme: dark)').matches)
    }
    
    if (savedPrimaryColor) {
      setPrimaryColor(savedPrimaryColor)
    }
    
    if (savedAccentColor) {
      setAccentColor(savedAccentColor)
    }
  }, [])

  useEffect(() => {
    // Apply theme to document
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    
    // Save theme preference
    localStorage.setItem('theme', isDark ? 'dark' : 'light')
  }, [isDark])

  useEffect(() => {
    // Apply color scheme
    document.documentElement.style.setProperty('--primary-color', primaryColor)
    document.documentElement.style.setProperty('--accent-color', accentColor)
    
    localStorage.setItem('primaryColor', primaryColor)
    localStorage.setItem('accentColor', accentColor)
  }, [primaryColor, accentColor])

  const toggleTheme = () => {
    setIsDark(!isDark)
  }

  const setTheme = (theme) => {
    setIsDark(theme === 'dark')
  }

  const updateColors = (primary, accent) => {
    if (primary) setPrimaryColor(primary)
    if (accent) setAccentColor(accent)
  }

  const value = {
    isDark,
    primaryColor,
    accentColor,
    toggleTheme,
    setTheme,
    updateColors
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
