import React from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  BookOpen, 
  TrendingUp, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Activity
} from 'lucide-react'

const AdminDashboard = () => {
  const stats = [
    { name: 'Total Users', value: '1,247', change: '+12%', icon: Users, color: 'bg-blue-500' },
    { name: 'Active Projects', value: '89', change: '+8%', icon: BookOpen, color: 'bg-green-500' },
    { name: 'System Health', value: '99.9%', change: '+0.1%', icon: Activity, color: 'bg-purple-500' },
    { name: 'Pending Reviews', value: '23', change: '-5%', icon: Clock, color: 'bg-yellow-500' }
  ]

  const recentActivity = [
    { type: 'user', message: 'New user registration: <PERSON>', time: '2 minutes ago', status: 'info' },
    { type: 'project', message: 'Project "AI Assistant" submitted for review', time: '15 minutes ago', status: 'warning' },
    { type: 'system', message: 'Database backup completed successfully', time: '1 hour ago', status: 'success' },
    { type: 'security', message: 'Failed login attempt detected', time: '2 hours ago', status: 'error' },
    { type: 'user', message: 'User "<PERSON>" upgraded to premium', time: '3 hours ago', status: 'success' }
  ]

  const systemAlerts = [
    { type: 'warning', message: 'Server CPU usage is above 80%', time: '5 minutes ago' },
    { type: 'info', message: 'Scheduled maintenance in 2 hours', time: '1 hour ago' },
    { type: 'success', message: 'All security scans passed', time: '6 hours ago' }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Admin Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Monitor and manage the ProjectSphere platform
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="card p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </p>
                <p className={`text-sm ${
                  stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change} from last month
                </p>
              </div>
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Activity
          </h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.status === 'success' ? 'bg-green-500' :
                  activity.status === 'warning' ? 'bg-yellow-500' :
                  activity.status === 'error' ? 'bg-red-500' :
                  'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">
                    {activity.message}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* System Alerts */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            System Alerts
          </h3>
          <div className="space-y-4">
            {systemAlerts.map((alert, index) => (
              <div key={index} className={`p-4 rounded-lg border ${
                alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800' :
                alert.type === 'success' ? 'border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800' :
                'border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800'
              }`}>
                <div className="flex items-start space-x-3">
                  {alert.type === 'warning' ? (
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  ) : alert.type === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  ) : (
                    <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {alert.message}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{alert.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="card p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { name: 'User Management', description: 'Manage user accounts and permissions', color: 'bg-blue-500' },
            { name: 'System Settings', description: 'Configure platform settings', color: 'bg-green-500' },
            { name: 'Analytics', description: 'View detailed analytics and reports', color: 'bg-purple-500' },
            { name: 'Backup & Security', description: 'Manage backups and security', color: 'bg-red-500' }
          ].map((action, index) => (
            <button
              key={index}
              className="p-4 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <div className={`${action.color} w-8 h-8 rounded-lg mb-3`}></div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                {action.name}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {action.description}
              </p>
            </button>
          ))}
        </div>
      </motion.div>
    </motion.div>
  )
}

export default AdminDashboard
