import React from 'react'
import { motion } from 'framer-motion'
import { Users, Plus, Search, Filter } from 'lucide-react'

const TeamBuilder = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Team Builder</h1>
          <p className="text-gray-600 dark:text-gray-400">Find and build amazing teams</p>
        </div>
        <button className="btn-primary flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Create Team</span>
        </button>
      </div>

      <div className="card p-8 text-center">
        <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Smart Team Matching
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          AI-powered team building based on skills, interests, and project requirements
        </p>
      </div>
    </motion.div>
  )
}

export default TeamBuilder
