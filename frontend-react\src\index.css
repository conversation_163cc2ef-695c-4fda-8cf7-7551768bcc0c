@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans dark:bg-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 active:from-primary-800 active:to-primary-900 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply btn bg-gradient-to-r from-secondary-600 to-secondary-700 text-white hover:from-secondary-700 hover:to-secondary-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary-300 text-primary-700 hover:bg-primary-50 hover:border-primary-400 dark:border-primary-600 dark:text-primary-400 dark:hover:bg-primary-900/20;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800;
  }
  
  .card {
    @apply rounded-xl border border-gray-200 bg-white shadow-sm hover:shadow-md transition-all duration-200 dark:border-gray-700 dark:bg-gray-800;
  }
  
  .card-hover {
    @apply card hover:shadow-lg hover:-translate-y-1 transform transition-all duration-300;
  }
  
  .input {
    @apply flex h-11 w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:border-primary-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }
  
  .glass {
    @apply backdrop-blur-md bg-white/80 border border-white/20 dark:bg-gray-900/80 dark:border-gray-700/20;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-500 via-secondary-500 to-accent-600;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 hover:text-primary-700 transition-all duration-200 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-primary-400;
  }
  
  .sidebar-item.active {
    @apply bg-primary-100 text-primary-700 border-r-2 border-primary-600 dark:bg-primary-900/30 dark:text-primary-400;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}
