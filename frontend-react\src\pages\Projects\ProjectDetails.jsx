import React from 'react'
import { motion } from 'framer-motion'
import { useParams } from 'react-router-dom'

const ProjectDetails = () => {
  const { id } = useParams()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <div className="card p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Project Details
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Detailed view for project ID: {id}
        </p>
        <p className="text-sm text-gray-500 mt-4">
          This page will show comprehensive project information, team members, files, progress tracking, and collaboration tools.
        </p>
      </div>
    </motion.div>
  )
}

export default ProjectDetails
